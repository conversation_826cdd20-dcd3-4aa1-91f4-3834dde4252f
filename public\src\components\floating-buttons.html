<!-- Floating Contact Buttons Component -->
<div class="fixed bottom-4 right-4 z-40 flex flex-col items-end space-y-3 floating-button-group">
  <!-- WhatsApp <PERSON><PERSON> (Always Visible) -->
  <a href="https://wa.me/447300888847" target="_blank" rel="noopener noreferrer" 
     class="flex items-center justify-center w-14 h-14 rounded-full bg-[#25D366] hover:bg-[#20c85a] text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 focus:outline-none group whatsapp-pulse"
     title="Contact us on WhatsApp"
     aria-label="Contact us on WhatsApp">
    <svg class="w-6 h-6 fill-white transition-transform duration-300 group-hover:scale-110" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
      <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488"/>
    </svg>
  </a>

  <!-- Back to Top Button -->
  <button id="back-to-top" class="flex items-center justify-center w-10 h-10 rounded-full bg-white dark:bg-gray-700 text-brand-purple dark:text-brand-peach shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 focus:outline-none opacity-0 invisible"
          aria-label="Back to top"
          title="Back to top">
    <span class="material-icons" aria-hidden="true">arrow_upward</span>
  </button>
</div>

<!-- JavaScript for the floating buttons -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Back to top functionality
    const backToTopButton = document.getElementById('back-to-top');

    if (backToTopButton) {
      // Show button when scrolled down
      let scrollTimeout;
      window.addEventListener('scroll', function() {
        clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(() => {
          if (window.scrollY > 300) {
            backToTopButton.classList.remove('opacity-0', 'invisible');
            backToTopButton.classList.add('opacity-100', 'visible');
          } else {
            backToTopButton.classList.add('opacity-0', 'invisible');
            backToTopButton.classList.remove('opacity-100', 'visible');
          }
        }, 10);
      });

      // Scroll to top when clicked
      backToTopButton.addEventListener('click', function(e) {
        e.preventDefault();
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      });
    }

    // Add pulse animation to WhatsApp button to draw attention
    const whatsappButton = document.querySelector('a[href*="wa.me"]');
    if (whatsappButton) {
      // Remove initial pulse class and add it after a delay
      whatsappButton.classList.remove('whatsapp-pulse');
      
      setTimeout(() => {
        whatsappButton.classList.add('whatsapp-pulse');
        // Remove pulse after 6 seconds
        setTimeout(() => {
          whatsappButton.classList.remove('whatsapp-pulse');
        }, 6000);
      }, 2000);

      // Add pulse again when user hovers over it
      whatsappButton.addEventListener('mouseenter', function() {
        this.classList.remove('whatsapp-pulse');
      });

      whatsappButton.addEventListener('mouseleave', function() {
        setTimeout(() => {
          this.classList.add('whatsapp-pulse');
          setTimeout(() => {
            this.classList.remove('whatsapp-pulse');
          }, 3000);
        }, 1000);
      });
    }

    console.log('Floating buttons initialized successfully');
  });
</script>
