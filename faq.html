<!DOCTYPE html>
<html lang="en" data-theme="light">
  <head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frequently Asked Questions | Paw Patroller UK</title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="Find answers to common questions about our dog walking, pet sitting, and pet care services at Paw Patroller UK.">
    <meta name="keywords" content="dog walking FAQ, pet sitting questions, pet care services, dog walker questions, pet care FAQ">
    <meta name="author" content="Paw Patroller UK">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://pawpatrolleruk.com/faq.html">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://pawpatrolleruk.com/faq.html">
    <meta property="og:title" content="Frequently Asked Questions | Paw Patroller UK">
    <meta property="og:description" content="Find answers to common questions about our dog walking, pet sitting, and pet care services.">
    <meta property="og:image" content="https://pawpatrolleruk.com/assets/hero-desktop.jpg">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://pawpatrolleruk.com/faq.html">
    <meta name="twitter:title" content="Frequently Asked Questions | Paw Patroller UK">
    <meta name="twitter:description" content="Find answers to common questions about our dog walking, pet sitting, and pet care services.">
    <meta name="twitter:image" content="https://pawpatrolleruk.com/assets/hero-desktop.jpg">

    <!-- Vite entry point -->
    <script type="module" src="/src/main.js"></script>

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Styles -->
    <link rel="stylesheet" href="src/css/index.css">
  </head>

  <body class="bg-brand-light dark:bg-gray-900">
    <!-- Navbar Container -->
    <div id="navbar-container"></div>

    <!-- Main Content -->
    <main>
      <!-- FAQ Header -->
      <section class="py-16 bg-gradient-to-r from-brand-purple/5 to-brand-peach/10 dark:from-brand-purple/20 dark:to-brand-purple-dark/30">
        <div class="container px-4 mx-auto">
          <div class="text-center max-w-3xl mx-auto mb-8">
            <span class="inline-block px-3 py-1 text-xs font-semibold tracking-wider text-brand-purple dark:text-brand-peach bg-brand-purple/10 dark:bg-brand-purple/20 rounded-full uppercase mb-4">Help Center</span>
            <h1 class="text-3xl sm:text-4xl md:text-5xl font-fredoka gradient-text mb-6">Frequently Asked Questions</h1>
            <p class="text-gray-600 dark:text-gray-300 text-lg leading-relaxed">Find answers to common questions about our pet care services. If you can't find what you're looking for, please don't hesitate to contact us.</p>
          </div>
        </div>
      </section>

      <!-- FAQ Content -->
      <section class="py-16 bg-white dark:bg-gray-800">
        <div class="container px-4 mx-auto">
          <div class="max-w-3xl mx-auto">
            <!-- FAQ Categories -->
            <div class="mb-12 flex flex-wrap justify-center gap-4">
              <button class="faq-category-btn active" data-category="all">All Questions</button>
              <button class="faq-category-btn" data-category="services">Services</button>
              <button class="faq-category-btn" data-category="booking">Booking</button>
              <button class="faq-category-btn" data-category="payment">Payment</button>
              <button class="faq-category-btn" data-category="safety">Safety</button>
            </div>

            <!-- FAQ Items -->
            <div class="space-y-6">
              <!-- Services FAQs -->
              <div class="faq-item" data-category="services">
                <button class="faq-question">
                  <span class="faq-question-text">What areas do you cover?</span>
                  <span class="material-icons faq-icon">add</span>
                </button>
                <div class="faq-answer">
                  <p class="mt-4">We are based in Putney and we cover Putney, Richmond, Barnes and Wandsworth areas. If you're not sure, give us a call and we will explore options for you.</p>
                </div>
              </div>

              <div class="faq-item" data-category="services">
                <button class="faq-question">
                  <span class="faq-question-text">Where do you walk?</span>
                  <span class="material-icons faq-icon">add</span>
                </button>
                <div class="faq-answer">
                  <p class="mt-4">We aim to diversify our walking routes, utilizing beautiful locations such as Richmond Park, Bushy Park, Putney Heath, and Wimbledon Common, along with their woodlands, fields, brooks, and ponds, as well as local parks. We strive to minimize the time dogs spend in the car.</p>
                </div>
              </div>

              <div class="faq-item" data-category="services">
                <button class="faq-question">
                  <span class="faq-question-text">How long are the walks?</span>
                  <span class="material-icons faq-icon">add</span>
                </button>
                <div class="faq-answer">
                  <p class="mt-4">Each walk lasts a minimum of 30 minutes or 1 hour, but frequently longer, depending on the walk you book. Please note that travel time is not included in the walking duration.</p>
                </div>
              </div>

              <div class="faq-item" data-category="services">
                <button class="faq-question">
                  <span class="faq-question-text">How many dogs can you walk at once?</span>
                  <span class="material-icons faq-icon">add</span>
                </button>
                <div class="faq-answer">
                  <p class="mt-4">We walk in small, well-managed groups to ensure personalized attention and care.</p>
                </div>
              </div>

              <!-- Safety FAQs -->
              <div class="faq-item" data-category="safety">
                <button class="faq-question">
                  <span class="faq-question-text">Do all employees have a current police check?</span>
                  <span class="material-icons faq-icon">add</span>
                </button>
                <div class="faq-answer">
                  <p class="mt-4">Yes, all of our employees who are responsible for both your dogs and homes have undergone a DBS check.</p>
                </div>
              </div>

              <div class="faq-item" data-category="safety">
                <button class="faq-question">
                  <span class="faq-question-text">Are you insured?</span>
                  <span class="material-icons faq-icon">add</span>
                </button>
                <div class="faq-answer">
                  <p class="mt-4">Yes, we carry specialised business insurance.</p>
                </div>
              </div>

              <div class="faq-item" data-category="services">
                <button class="faq-question">
                  <span class="faq-question-text">Do you have any testimonials?</span>
                  <span class="material-icons faq-icon">add</span>
                </button>
                <div class="faq-answer">
                  <p class="mt-4">Absolutely! Testimonials from our satisfied clients are available on our website.</p>
                </div>
              </div>

              <div class="faq-item" data-category="services">
                <button class="faq-question">
                  <span class="faq-question-text">Can you provide references?</span>
                  <span class="material-icons faq-icon">add</span>
                </button>
                <div class="faq-answer">
                  <p class="mt-4">References can be provided upon request.</p>
                </div>
              </div>

              <!-- Payment FAQs -->
              <div class="faq-item" data-category="payment">
                <button class="faq-question">
                  <span class="faq-question-text">How much do you charge?</span>
                  <span class="material-icons faq-icon">add</span>
                </button>
                <div class="faq-answer">
                  <p class="mt-4">Please refer to our price list for detailed information.</p>
                </div>
              </div>

              <div class="faq-item" data-category="services">
                <button class="faq-question">
                  <span class="faq-question-text">Do you pick up and drop off my dog?</span>
                  <span class="material-icons faq-icon">add</span>
                </button>
                <div class="faq-answer">
                  <p class="mt-4">Yes, we offer pick-up and drop-off services for your dog from your home.</p>
                </div>
              </div>

              <div class="faq-item" data-category="payment">
                <button class="faq-question">
                  <span class="faq-question-text">What payment methods do you accept?</span>
                  <span class="material-icons faq-icon">add</span>
                </button>
                <div class="faq-answer">
                  <p class="mt-4">We accept cash, bank transfers, and payments via PayPal.</p>
                </div>
              </div>

              <div class="faq-item" data-category="payment">
                <button class="faq-question">
                  <span class="faq-question-text">When is payment due? Do you require a deposit?</span>
                  <span class="material-icons faq-icon">add</span>
                </button>
                <div class="faq-answer">
                  <p class="mt-4">We will provide a quote for the service before beginning pet sitting, and payment is due in full within 24 hours after the service is completed. No deposit is required.</p>
                </div>
              </div>

              <!-- Booking FAQs -->
              <div class="faq-item" data-category="booking">
                <button class="faq-question">
                  <span class="faq-question-text">What happens if my return home gets delayed?</span>
                  <span class="material-icons faq-icon">add</span>
                </button>
                <div class="faq-answer">
                  <p class="mt-4">If your return home is delayed, simply message us, and we will continue to care for your animals until you arrive.</p>
                </div>
              </div>

              <div class="faq-item" data-category="booking">
                <button class="faq-question">
                  <span class="faq-question-text">How many days in advance do I need to contact you?</span>
                  <span class="material-icons faq-icon">add</span>
                </button>
                <div class="faq-answer">
                  <p class="mt-4">While a day or two notice is preferred, we are flexible with sudden changes and can accommodate urgent requests. Just send us a message, and we can begin care as early as that day if needed.</p>
                </div>
              </div>

              <div class="faq-item" data-category="services">
                <button class="faq-question">
                  <span class="faq-question-text">What types of animals do you care for?</span>
                  <span class="material-icons faq-icon">add</span>
                </button>
                <div class="faq-answer">
                  <p class="mt-4">We are happy to care for dogs, cats, birds of all types, reptiles, and pocket pets such as guinea pigs and rats. However, we do not provide pet sitting services for horses, cows, or other large farm animals.</p>
                </div>
              </div>

              <div class="faq-item" data-category="booking">
                <button class="faq-question">
                  <span class="faq-question-text">How do I become a client?</span>
                  <span class="material-icons faq-icon">add</span>
                </button>
                <div class="faq-answer">
                  <p class="mt-4">To become a client, please call 073 0088 8847 <NAME_EMAIL>. We will arrange a convenient time to visit you.</p>
                </div>
              </div>
            </div>

            <!-- Contact Section -->
            <div class="mt-16 p-8 bg-brand-light dark:bg-gray-700 rounded-xl text-center">
              <h3 class="text-2xl font-fredoka text-brand-purple dark:text-brand-peach mb-4">Still Have Questions?</h3>
              <p class="mb-6 dark:text-gray-200">We're here to help! Contact us directly and we'll be happy to assist you.</p>
              <a href="index.html#contact" class="inline-flex items-center px-6 py-3 text-base font-medium text-white transition-all duration-300 transform rounded-full bg-brand-purple hover:bg-brand-purple-dark dark:bg-brand-purple-dark dark:hover:bg-brand-purple hover:shadow-lg hover:-translate-y-1">
                <span class="material-icons mr-2">contact_support</span>
                Contact Us
              </a>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- Footer Container -->
    <div id="footer-container"></div>

    <!-- Floating Buttons Container -->
    <div id="floating-buttons-container"></div>

    <!-- Cookie Banner Container -->
    <div id="cookie-banner-container"></div>

    <!-- FAQ JavaScript -->
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // FAQ Accordion functionality
        const faqQuestions = document.querySelectorAll('.faq-question');

        faqQuestions.forEach(question => {
          question.addEventListener('click', function() {
            const answer = this.nextElementSibling;
            const icon = this.querySelector('.faq-icon');

            // Toggle current FAQ item
            this.classList.toggle('active');

            if (this.classList.contains('active')) {
              answer.style.maxHeight = answer.scrollHeight + 'px';
              icon.textContent = 'remove';
            } else {
              answer.style.maxHeight = '0';
              icon.textContent = 'add';
            }
          });
        });

        // FAQ Category filtering
        const categoryButtons = document.querySelectorAll('.faq-category-btn');
        const faqItems = document.querySelectorAll('.faq-item');

        categoryButtons.forEach(button => {
          button.addEventListener('click', function() {
            const category = this.dataset.category;

            // Update active button
            categoryButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Filter FAQ items
            faqItems.forEach(item => {
              if (category === 'all' || item.dataset.category === category) {
                item.style.display = 'block';
              } else {
                item.style.display = 'none';
              }
            });
          });
        });
      });
    </script>

    <style>
      /* FAQ Styles */
      .faq-category-btn {
        padding: 0.5rem 1rem;
        border-radius: 9999px;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.3s;
        background-color: #f5f5f5;
        color: #6a1b9a;
      }

      .dark .faq-category-btn {
        background-color: #333;
        color: #ffccbc;
      }

      .faq-category-btn.active {
        background-color: #6a1b9a;
        color: white;
      }

      .dark .faq-category-btn.active {
        background-color: #4a148c;
        color: white;
      }

      .faq-item {
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        overflow: hidden;
      }

      .dark .faq-item {
        border-color: #444;
      }

      .faq-question {
        width: 100%;
        padding: 1rem 1.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: white;
        cursor: pointer;
        transition: all 0.3s;
        text-align: left;
      }

      .dark .faq-question {
        background-color: #1e1e1e;
        color: #e0e0e0;
      }

      .faq-question:hover {
        background-color: #f9fafb;
      }

      .dark .faq-question:hover {
        background-color: #2a2a2a;
      }

      .faq-question.active {
        background-color: #f9fafb;
      }

      .dark .faq-question.active {
        background-color: #2a2a2a;
      }

      .faq-question-text {
        font-weight: 500;
        font-size: 1.125rem;
      }

      .faq-answer {
        padding: 0 1.5rem;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-out;
        background-color: #f9fafb;
      }

      .dark .faq-answer {
        background-color: #2a2a2a;
        color: #e0e0e0;
      }

      .faq-answer p {
        padding-top: 1rem;
        padding-bottom: 1rem;
      }
    </style>
  </body>
</html>
