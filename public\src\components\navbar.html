<!-- Top Social Media Bar -->
<div class="social-top-bar bg-brand-purple text-white py-2 hidden lg:block">
  <div class="container mx-auto px-4">
    <div class="flex items-center justify-between text-sm">
      <div class="flex items-center gap-4">
        <span class="flex items-center gap-2">
          <span class="material-icons text-sm">phone</span>
          <a href="tel:07300888847" class="hover:text-brand-peach transition-colors">07300 888 847</a>
        </span>
      </div>
      <div class="flex items-center gap-3">
        <span class="text-xs opacity-75">Follow us:</span>
        <a href="https://www.instagram.com/Paw_Patroller_UK" target="_blank" rel="noopener noreferrer"
          class="social-top-link hover:text-brand-peach transition-colors" aria-label="Instagram">
          <svg class="w-4 h-4 fill-current" viewBox="0 0 24 24">
            <path
              d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z">
            </path>
          </svg>
        </a>
        <a href="https://www.facebook.com/profile.php?id=61571211915327" target="_blank" rel="noopener noreferrer"
          class="social-top-link hover:text-brand-peach transition-colors" aria-label="Facebook">
          <svg class="w-4 h-4 fill-current" viewBox="0 0 24 24">
            <path
              d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z">
            </path>
          </svg>
        </a>
        <a href="https://wa.me/447300888847" target="_blank" rel="noopener noreferrer"
          class="social-top-link hover:text-brand-peach transition-colors" aria-label="WhatsApp">
          <svg class="w-4 h-4 fill-current" viewBox="0 0 24 24">
            <path
              d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488">
            </path>
          </svg>
        </a>
      </div>
    </div>
  </div>
</div>

<!-- Main Navigation -->
<nav class="navbar-component sticky top-0 z-50 bg-white/95 shadow-md backdrop-blur-md">
  <div class="container mx-auto px-4">
    <div class="flex items-center justify-between py-4">
      <!-- Logo -->
      <div class="logo">
        <a href="index.html" class="text-xl font-semibold sm:text-2xl gradient-text transition-all duration-300 hover:scale-105 font-agent-orange">
          Paw Patroller UK
        </a>
      </div>

      <!-- Desktop Navigation Links -->
      <div class="hidden lg:flex items-center gap-8">
        <ul class="menu flex gap-6">
          <li><a href="index.html#home" id="nav-home" class="nav-link">Home</a></li>
          <li><a href="index.html#about" id="nav-about" class="nav-link">About</a></li>
          <li><a href="index.html#services" id="nav-services" class="nav-link">Services</a></li>
          <li><a href="testimonials.html" id="nav-testimonials" class="nav-link">Testimonials</a></li>
          <li><a href="faq.html" id="nav-faq" class="nav-link">FAQs</a></li>
          <li><a href="index.html#contact" id="nav-contact" class="nav-link">Contact</a></li>
        </ul>
      </div>

      <!-- Mobile Menu and Contact -->
      <div class="flex items-center gap-2 relative">
        <!-- Mobile Phone Icon -->
        <a href="tel:07300888847" class="action-btn lg:hidden" aria-label="Call">
          <span class="material-icons text-lg">phone</span>
        </a>

        <!-- Mobile Menu Toggle -->
        <input type="checkbox" id="nav-toggle" class="hidden peer lg:hidden">
        <label for="nav-toggle" class="hamburger cursor-pointer z-50 lg:hidden p-2 -m-2">
          <span class="block w-6 h-0.5 bg-gray-700 transition-transform duration-300"></span>
          <span class="block w-6 h-0.5 bg-gray-700 mt-1.5 transition-opacity duration-300"></span>
          <span class="block w-6 h-0.5 bg-gray-700 mt-1.5 transition-transform duration-300"></span>
        </label>

        <!-- Mobile Navigation Menu -->
        <div id="nav-menu"
          class="hidden absolute top-full right-0 w-full max-w-sm bg-white/95 lg:hidden peer-checked:block">
          <div class="container mx-auto px-4 py-4 border-t border-gray-200">
            <!-- Mobile Nav Links -->
            <ul class="menu flex flex-col gap-4">
              <li><a href="index.html#home" id="nav-home-mobile" class="nav-link">Home</a></li>
              <li><a href="index.html#about" id="nav-about-mobile" class="nav-link">About</a></li>
              <li><a href="index.html#services" id="nav-services-mobile" class="nav-link">Services</a></li>
              <li><a href="testimonials.html" id="nav-testimonials-mobile" class="nav-link">Testimonials</a></li>
              <li><a href="faq.html" id="nav-faq-mobile" class="nav-link">FAQs</a></li>
              <li><a href="index.html#contact" id="nav-contact-mobile" class="nav-link">Contact</a></li>
            </ul>
            <!-- Mobile Social Links -->
            <div class="flex items-center justify-center gap-4 mt-4 pt-4 border-t border-gray-200">
              <a href="https://www.instagram.com/Paw_Patroller_UK" target="_blank" rel="noopener noreferrer"
                class="action-btn social-link" aria-label="Instagram">
                <svg class="w-4 h-4 fill-current" viewBox="0 0 24 24">
                  <path
                    d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z">
                  </path>
                </svg>
              </a>
              <a href="https://www.facebook.com/profile.php?id=61571211915327" target="_blank" rel="noopener noreferrer"
                class="action-btn social-link" aria-label="Facebook">
                <svg class="w-4 h-4 fill-current" viewBox="0 0 24 24">
                  <path
                    d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z">
                  </path>
                </svg>
              </a>
              <a href="https://wa.me/447300888847" target="_blank" rel="noopener noreferrer"
                class="action-btn social-link" aria-label="WhatsApp">
                <svg class="w-4 h-4 fill-current" viewBox="0 0 24 24">
                  <path
                    d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488">
                  </path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</nav>
<script>
  // Enhanced mobile menu functionality
  document.addEventListener('DOMContentLoaded', function() {
    const navToggle = document.getElementById('nav-toggle');
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.getElementById('nav-menu');

    // Ensure hamburger is clickable and functional
    if (hamburger && navToggle) {
      hamburger.addEventListener('click', function(e) {
        e.preventDefault();
        navToggle.checked = !navToggle.checked;
      });
    }

    // Close mobile menu when any link inside the menu is clicked
    document.querySelectorAll('.nav-link').forEach(link => {
      link.addEventListener('click', () => {
        if (navToggle) {
          navToggle.checked = false;
        }
      });
    });

    // Close menu when clicking outside
    document.addEventListener('click', function(e) {
      if (navToggle && navToggle.checked) {
        if (!navMenu.contains(e.target) && !hamburger.contains(e.target)) {
          navToggle.checked = false;
        }
      }
    });
  });
</script>
<!--
  Custom CSS for layout is handled in the main CSS files and/or Tailwind classes.
  For further design, see the design system or add custom classes as needed.
-->
