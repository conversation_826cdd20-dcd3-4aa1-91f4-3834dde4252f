/**
 * Testimonials and review card styles
 */

/* Review Card Styles */
.review-card {
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 15px;
  background-color: #fff;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.review-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.review-image {
  width: 100%;
  height: 200px;
  object-fit: contain;
  object-position: center;
  background-color: #f8f9fa;
  transition: transform 0.3s ease;
}

.review-image:hover {
  transform: scale(1.02);
}

.review-image-placeholder {
  width: 100%;
  height: 200px;
  background: linear-gradient(135deg, var(--brand-purple) 0%, #9c27b0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.review-image-placeholder span {
  color: white;
  font-size: 3rem;
}

.review-stars {
  display: flex;
  justify-content: center;
  padding: 0.75rem 0;
  background-color: rgba(106, 27, 154, 0.05);
}

.star {
  font-size: 1.5rem;
  color: #ffd700; /* Golden color for stars */
  margin: 0 2px;
}

.review-content {
  padding: 1rem;
}

.review-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.review-name {
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--brand-purple);
}

.review-badge {
  padding: 0.25rem 0.5rem;
  background-color: #4caf50;
  color: white;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.review-text {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #444;
  background-color: #f9f9f9;
  padding: 1rem;
  border-radius: 8px;
  position: relative;
  max-height: 200px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--brand-purple) #f0f0f0;
}

.review-text::-webkit-scrollbar {
  width: 8px;
}

.review-text::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 8px;
}

.review-text::-webkit-scrollbar-thumb {
  background-color: var(--brand-purple);
  border-radius: 8px;
}



@media screen and (max-width: 640px) {
  .review-image, .review-image-placeholder {
    height: 150px;
  }
  
  .star {
    font-size: 1.25rem;
  }
}

/* Review Container Carousel Styles */
.review-carousel-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  padding: 0 1rem 2rem 1rem; /* Added bottom padding for shadow space */
}

/* Social proof section with shadow space */
.review-carousel-container .mt-12 {
  padding-bottom: 1rem; /* Extra space for shadows */
}

/* Improved review carousel */
.review-carousel {
  display: flex;
  overflow-x: auto;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  gap: 1.5rem;
  padding: 1rem 0.5rem;
  scroll-snap-type: x mandatory;
  scroll-padding: 0 1rem;
  position: relative;
  /* Important: prevent touch event issues */
  touch-action: pan-x;
  user-select: none;
  /* Ensure all cards are aligned properly */
  align-items: stretch;
}

.review-carousel::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

.review-carousel .review-card {
  flex: 0 0 calc(100% - 2rem);
  scroll-snap-align: center;
  max-width: 400px;
  /* Ensure consistent height and alignment */
  min-height: 350px;
  display: flex;
  flex-direction: column;
  align-self: stretch;
}

@media (min-width: 768px) {
  .review-carousel .review-card {
    flex: 0 0 calc(50% - 1rem);
  }
}

@media (min-width: 1024px) {
  .review-carousel .review-card {
    flex: 0 0 calc(33.333% - 1rem);
  }
}

/* Improved Carousel Navigation */
.carousel-nav {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 1.5rem 0;
  gap: 1rem;
  padding: 0.5rem;
}

.carousel-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background-color: white;
  color: var(--brand-purple);
  border: 2px solid var(--brand-purple);
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 10; /* Ensure buttons are clickable */
}

.carousel-button:hover:not(:disabled) {
  background-color: var(--brand-purple);
  color: white;
  transform: translateY(-2px);
}

.carousel-button:active:not(:disabled) {
  transform: translateY(0);
}

.carousel-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  border-color: #ccc;
  color: #999;
  background-color: #f5f5f5;
}

/* Pagination dots */
.pagination-dots {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin: 0 1rem;
}

.pagination-dot {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  background-color: #ddd;
  cursor: pointer;
  transition: none;
}

.pagination-dot:hover {
  background-color: #bbb;
}

.pagination-dot.active {
  background-color: var(--brand-purple);
  transform: scale(1.2);
}

/* Ensure social media links have proper shadow space */
.review-carousel-container a[href*="facebook"],
.review-carousel-container a[href*="instagram"] {
  margin: 0.5rem; /* Add margin to prevent shadow clipping */
}

/* Review Image Container for better error handling */
.review-image-container {
  width: 100%;
  height: 200px;
  background-color: #f0f0f0;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: flex-start;
}

@media screen and (max-width: 640px) {
  .review-image-container {
    height: 150px;
  }
  
  .review-image {
    height: 150px;
  }
}

/* Alternative review image style to prevent cropping */
.review-image-full {
  width: 100%;
  height: 200px;
  object-fit: contain;
  object-position: center;
  background-color: #f8f9fa;
  transition: transform 0.3s ease;
}

.review-image-full:hover {
  transform: scale(1.02);
}

/* Use this class for portrait images that get cropped */
.review-image-portrait {
  width: 100%;
  height: 200px;
  object-fit: cover;
  object-position: center 20%;
  transition: transform 0.3s ease;
}

.review-image-portrait:hover {
  transform: scale(1.05);
}

/* Enhanced image handling to show full image without cropping */
.review-image-container img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: center;
  background-color: #f8f9fa;
}

/* Ensure the container has a nice background for images with different aspect ratios */
.review-image-container {
  width: 100%;
  height: 200px;
  background-color: #f8f9fa;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Special handling for portrait images - still show full image */
.review-image-container img[src*=".jpg"]:not([src*="default"]) {
  object-fit: contain;
  object-position: center;
}

/* Fallback for images that fail to load */
.review-image-container img[src*="default-review.jpg"] {
  object-fit: contain;
  object-position: center;
  background-color: #f0f0f0;
}
