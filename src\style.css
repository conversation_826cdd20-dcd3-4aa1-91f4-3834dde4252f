@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Styles */
:root {
  --brand-purple: #6a1b9a;
  --brand-purple-dark: #4a148c;
  --brand-light: #f5f5f5;
  --brand-peach: #ffccbc;
}

/* Custom Animation Keyframes */
@keyframes float-slow {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(5deg); }
}

@keyframes float-medium {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(-5deg); }
}

@keyframes float-fast {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(8deg); }
}

@keyframes fade-in-up {
  0% { opacity: 0; transform: translateY(20px); }
  100% { opacity: 1; transform: translateY(0); }
}

/* Animation Classes */
.animate-float-slow {
  animation: float-slow 8s ease-in-out infinite;
}

.animate-float-medium {
  animation: float-medium 6s ease-in-out infinite;
}

.animate-float-fast {
  animation: float-fast 4s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fade-in-up 1s ease-out forwards;
}

html {
  scroll-behavior: smooth;
}

@supports (-webkit-overflow-scrolling: touch) {
  html {
    scroll-behavior: auto;
  }
}

body {
  font-family: 'Poppins', sans-serif;
  margin: 0;
  min-width: 320px;
}

/* Clean Mobile-First Navbar & Dropdown Implementation */
.navbar {
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
}

.navbar-start {
  @apply flex items-center;
}

.navbar-center {
  display: none; /* Hidden by default on mobile */
}

.navbar-end {
  @apply flex items-center justify-end;
}

.menu {
  @apply flex p-0 m-0 list-none;
}

.menu-horizontal {
  @apply flex-row;
}

.menu li {
  @apply list-none;
}

.menu li a {
  @apply block px-3 py-2 text-gray-700 transition-all duration-200 hover:text-brand-purple;
}

/* Mobile Dropdown */
.mobile-dropdown {
  position: static;
}

.mobile-dropdown-button {
  @apply btn btn-ghost btn-circle;
  cursor: pointer;
}

.mobile-dropdown-content {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  width: 90%;
  max-width: 90vw;
  margin: 0.5rem auto;
  background-color: white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 0.5rem;
  padding: 1rem;
  z-index: 100;
}

/* Enhanced vertical menu styling */
.mobile-dropdown-content .menu {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0;
}

.mobile-dropdown-content .menu li {
  width: 100%;
  margin-bottom: 0.25rem;
}

.mobile-dropdown-content .menu li:last-child {
  margin-bottom: 0;
}

.mobile-dropdown-content .menu li a {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  font-weight: 500;
}

.mobile-dropdown-content .menu li a:hover,
.mobile-dropdown-content .menu li a:focus {
  background-color: rgba(106, 27, 154, 0.1);
  color: var(--brand-purple);
}

.mobile-dropdown-content.open {
  display: block;
  animation: slideDown 0.3s ease-out;
}

/* Desktop View Adjustments */
@media (min-width: 1024px) {
  .navbar-center {
    display: flex;
    align-items: center;
  }

  .mobile-dropdown {
    display: none;
  }
}

/* --- Desktop: Apply mobile button style to desktop action buttons --- */
@media (min-width: 1024px) {
  .action-btn {
    @apply btn btn-ghost btn-circle;
    cursor: pointer;
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background: transparent;
    border: none;
  }
  .action-btn:hover {
    transform: scale(1.1);
    background: rgba(106, 27, 154, 0.08);
  }
}

/* --- Desktop: Add drop shadow to certificates --- */
@media (min-width: 1024px) {
  .footer-cert-item {
    box-shadow: 0 4px 16px rgba(0,0,0,0.18);
    background: rgba(255,255,255,0.08);
    border-radius: 8px;
    padding: 0.5rem 1rem;
  }
  .footer-cert-icon {
    box-shadow: 0 2px 8px rgba(0,0,0,0.18);
    background: white;
  }
}

/* --- Desktop: Hero section button style and trust indicator shadow --- */
@media (min-width: 1024px) {
  #home.hero a[href="#services"],
  #home.hero a[href="#contact"] {
    border-radius: 9999px;
    box-shadow: 0 4px 16px rgba(106, 27, 154, 0.18), 0 2px 8px rgba(0,0,0,0.10);
    padding: 0.75rem 2.5rem;
    font-weight: 600;
    font-size: 1.15rem;
    transition: transform 0.2s, box-shadow 0.2s;
  }
  #home.hero a[href="#services"]:hover,
  #home.hero a[href="#contact"]:hover {
    transform: translateY(-2px) scale(1.04);
    box-shadow: 0 8px 24px rgba(106, 27, 154, 0.22), 0 4px 16px rgba(0,0,0,0.15);
  }
  #home.hero .flex.items-center {
    box-shadow: 0 4px 16px rgba(0,0,0,0.18);
    background: rgba(255,255,255,0.10);
    border-radius: 12px;
    padding: 0.5rem 1.25rem;
    margin: 0 0.5rem;
  }
}

html {
  scroll-behavior: smooth;
}

@supports (-webkit-overflow-scrolling: touch) {
  html {
    scroll-behavior: auto;
  }
}

body {
  font-family: 'Poppins', sans-serif;
  margin: 0;
  min-width: 320px;
  overflow-x: hidden; /* Prevent horizontal scrolling */
}

/* Button styles */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 font-medium transition-all duration-300 rounded cursor-pointer;
}

.btn-ghost {
  @apply text-gray-700 bg-transparent hover:bg-gray-100;
}

.btn-circle {
  @apply flex items-center justify-center w-10 h-10 p-0 rounded-full;
}

button {
  cursor: pointer;
}

.divider {
  @apply h-px my-2 bg-gray-200;
}

/* More utility classes */
.gradient-text {
  background: linear-gradient(90deg, var(--brand-purple) 0%, #9c27b0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* Fix for gradient text getting cut off */
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  line-height: 1.3;
  display: inline-block; /* Ensures the background covers the entire text */
  width: 100%;
}

/* Fix for section headings */
h2.gradient-text {
  margin-top: 0.5rem;
  margin-bottom: 1.5rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

/* Responsive adjustments for headings */
@media screen and (max-width: 640px) {
  h2.gradient-text {
    line-height: 1.4;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    word-break: break-word;
  }
}

.hero {
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
}

/* Move hero background to overflow left and show right side on mobile */
@media screen and (max-width: 767px) {
  .hero {
    background-position: -30vw center;
    background-size: cover;
    background-repeat: no-repeat;
  }
}

/* Badge styles */
.badge-card {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 150px;
  min-height: 60px;
}

.badge-card img {
  transition: transform 0.3s ease;
}

.badge-card:hover img {
  transform: scale(1.05);
}

@media screen and (max-width: 480px) {
  .badge-card {
    min-width: 120px;
    min-height: 50px;
  }
}

/* Review Card Styles */
.review-card {
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 15px;
  background-color: #fff;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  /* Ensure consistent height and proper alignment */
  display: flex;
  flex-direction: column;
  min-height: 350px;
}

.review-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.review-image {
  width: 100%;
  height: 200px;
  object-fit: contain;
  object-position: center;
  background-color: #f8f9fa;
}

.review-image-placeholder {
  width: 100%;
  height: 200px;
  background: linear-gradient(135deg, var(--brand-purple) 0%, #9c27b0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.review-image-placeholder span {
  color: white;
  font-size: 3rem;
}

.review-stars {
  display: flex;
  justify-content: center;
  padding: 0.75rem 0;
  background-color: rgba(106, 27, 154, 0.05);
}

.star {
  font-size: 1.5rem;
  color: #ffd700; /* Golden color for stars */
  margin: 0 2px;
}

.review-content {
  padding: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.review-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.review-name {
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--brand-purple);
}

.review-badge {
  padding: 0.25rem 0.5rem;
  background-color: #4caf50;
  color: white;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.review-text {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #444;
  background-color: #f9f9f9;
  padding: 1rem;
  border-radius: 8px;
  position: relative;
  max-height: 200px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--brand-purple) #f0f0f0;
}

.review-text::-webkit-scrollbar {
  width: 8px;
}

.review-text::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 8px;
}

.review-text::-webkit-scrollbar-thumb {
  background-color: var(--brand-purple);
  border-radius: 8px;
}

.review-text::before {
  content: '"';
  font-size: 1.5rem;
  color: #ccc;
  position: absolute;
  top: 0.25rem;
  left: 0.5rem;
}

.review-text::after {
  content: '"';
  font-size: 1.5rem;
  color: #ccc;
  position: absolute;
  bottom: -0.5rem;
  right: 0.5rem;
}

@media screen and (max-width: 640px) {
  .review-image, .review-image-placeholder {
    height: 150px;
  }

  .star {
    font-size: 1.25rem;
  }
}

/* Review Container Carousel Styles */
.review-carousel-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  padding: 0 1rem;
}

/* Improved review carousel */
.review-carousel {
  display: flex;
  overflow-x: auto;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  gap: 1.5rem;
  padding: 1rem 0.5rem;
  scroll-snap-type: x mandatory;
  scroll-padding: 0 1rem;
  position: relative;
  /* Important: prevent touch event issues */
  touch-action: pan-x;
  user-select: none;
  /* Ensure all cards are aligned properly */
  align-items: stretch;
}

.review-carousel::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

.review-carousel .review-card {
  flex: 0 0 calc(100% - 2rem);
  scroll-snap-align: center;
  max-width: 400px;
  /* Ensure consistent height and alignment */
  align-self: stretch;
}

@media (min-width: 768px) {
  .review-carousel .review-card {
    flex: 0 0 calc(50% - 1rem);
    scroll-snap-align: start;
  }
}

@media (min-width: 1024px) {
  .review-carousel .review-card {
    flex: 0 0 calc(33.333% - 1rem);
    scroll-snap-align: start;
  }
}

/* Improved Carousel Navigation */
.carousel-nav {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 1.5rem 0;
  gap: 1rem;
  padding: 0.5rem;
}

.carousel-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background-color: white;
  color: var(--brand-purple);
  border: 2px solid var(--brand-purple);
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 10; /* Ensure buttons are clickable */
}

.carousel-button:hover:not(:disabled) {
  background-color: var(--brand-purple);
  color: white;
  transform: translateY(-2px);
}

.carousel-button:active:not(:disabled) {
  transform: translateY(0);
}

.carousel-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  border-color: #ccc;
  color: #999;
  background-color: #f5f5f5;
}

/* Ensure buttons are clearly visible on all screen sizes */
@media (min-width: 768px) {
  .carousel-button {
    width: 3.5rem;
    height: 3.5rem;
  }
}

/* Carousel Navigation */
.carousel-nav {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 1.5rem 0;
  gap: 1rem;
}

.carousel-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background-color: white;
  color: var(--brand-purple);
  border: 2px solid var(--brand-purple);
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.carousel-button:hover {
  background-color: var(--brand-purple);
  color: white;
}

.carousel-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Pagination Indicators */
.pagination-dots {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin: 1rem 0;
}

.pagination-dot {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  background-color: #e0e0e0;
  cursor: pointer;
  transition: none;
}

.pagination-dot.active {
  background-color: var(--brand-purple);
  transform: scale(1.2);
}

/* Scroll Indicator Animation */
.scroll-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1rem 0;
  color: #666;
  font-size: 0.875rem;
}

.scroll-indicator .material-icons {
  animation: bounce-horizontal 2s infinite;
}

@keyframes bounce-horizontal {
  0%, 100% {
    transform: translateX(-5px);
  }
  50% {
    transform: translateX(5px);
  }
}

/* Review Image Container for better error handling */
.review-image-container {
  width: 100%;
  height: 200px;
  background-color: #f0f0f0;
  position: relative;
  overflow: hidden;
}

.review-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.review-image:hover {
  transform: scale(1.05);
}

@media screen and (max-width: 640px) {
  .review-image-container {
    height: 150px;
  }
}

/* Mobile-specific improvements for touch targets */
@media screen and (max-width: 768px) {
  /* Ensure all interactive elements meet WCAG touch target guidelines (44x44px minimum) */
  .btn, 
  button, 
  a[href], 
  input[type="button"], 
  input[type="submit"],
  .carousel-button,
  .pagination-dot {
    min-height: 44px;
    min-width: 44px;
    position: relative;
  }
  
  /* Service cards mobile improvements */
  .card {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }
  
  /* Better mobile typography */
  h1 { font-size: 2rem; line-height: 1.2; }
  h2 { font-size: 1.75rem; line-height: 1.3; }
  h3 { font-size: 1.5rem; line-height: 1.3; }
  
  /* Mobile-friendly hero section */
  .hero h1 {
    font-size: 2.5rem;
    line-height: 1.2;
    margin-bottom: 1rem;
  }
  
  .hero p {
    font-size: 1.1rem;
    line-height: 1.5;
    margin-bottom: 2rem;
  }
  
  /* Better mobile service card layout */
  details summary {
    padding: 1rem;
    font-size: 1.1rem;
    line-height: 1.4;
  }
  
  /* Mobile testimonials improvements */
  .review-card {
    margin: 0 0.5rem;
    width: calc(100% - 1rem);
    max-width: 350px;
    min-height: 300px; /* Smaller min-height for mobile */
  }

  /* Improve mobile carousel scrolling */
  .review-carousel {
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
  }

  .review-carousel .review-card {
    flex: 0 0 calc(100% - 1rem);
    scroll-snap-align: center;
  }
  
  /* Better mobile footer */
  .footer-contact-item {
    padding: 1rem 0;
  }
  
  /* Improve mobile floating buttons */
  .floating-button-group {
    bottom: 1rem;
    right: 1rem;
  }
  
  .floating-button-group a,
  .floating-button-group button {
    min-height: 56px;
    min-width: 56px;
    margin-bottom: 0.75rem;
  }
}

/* Improve focus indicators for keyboard navigation */
.btn:focus-visible,
button:focus-visible,
a:focus-visible,
summary:focus-visible {
  outline: 3px solid var(--brand-purple);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Better touch feedback for mobile */
@media (hover: none) and (pointer: coarse) {
  .btn:active,
  button:active,
  a:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }
}
