/**
 * Navigation styles
 */

/* Social Top Bar Styles */
.social-top-bar {
  position: relative;
  z-index: 60;
}

.social-top-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  transition: all 0.3s ease;
  background-color: rgba(255, 255, 255, 0.1);
}

.social-top-link:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Animation Keyframes for Navigation */
@keyframes slideDown {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1);
  }
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes scaleUp {
  0% { transform: translateX(-50%) scale(0.95); }
  100% { transform: translateX(-50%) scale(1); }
}

@keyframes borderPulse {
  0% { border-color: transparent; }
  50% { border-color: var(--brand-purple); }
  100% { border-color: transparent; }
}

/* Modern Mobile-First Navbar Implementation */
.navbar {
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
  transition: all 0.3s ease;
}

.navbar-start {
  @apply flex items-center gap-2;
}

.navbar-center {
  display: none; /* Hidden by default on mobile */
}

.navbar-end {
  @apply flex items-center justify-end gap-2;
}

/* Mobile Navigation - Show dropdown button, hide desktop menu */
@media (max-width: 1023px) {
  .mobile-dropdown {
    display: block !important;
  }
  
  .navbar-center {
    display: none !important;
  }
  
  /* Better touch targets for mobile */
  .mobile-dropdown-button {
    min-width: 44px;
    min-height: 44px;
  }
  
  .mobile-dropdown-content .menu li a {
    min-height: 44px;
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
  }
  
  /* Ensure dropdown doesn't overflow on small screens */
  .mobile-dropdown-content {
    max-height: 70vh;
    overflow-y: auto;
  }
}

/* Tablet Navigation */
@media (min-width: 768px) and (max-width: 1023px) {
  .mobile-dropdown-content {
    width: 60%;
    max-width: 400px;
  }
}

.menu {
  @apply flex p-0 m-0 list-none;
}

.menu-horizontal {
  @apply flex-row;
}

.menu li {
  @apply list-none relative;
  position: relative;
}

/* Modern menu item styling with active indicator */
.menu li a {
  @apply block px-3 py-2 text-gray-700 transition-all duration-300 hover:text-brand-purple;
  position: relative;
}

/* Animated underline effect for desktop menu */
.menu li a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 50%;
  background: linear-gradient(90deg, var(--brand-purple) 0%, #9c27b0 100%);
  transition: all 0.3s ease;
  transform: translateX(-50%);
  opacity: 0;
}

.menu li a:hover::after,
.menu li a.active::after {
  width: 80%;
  opacity: 1;
}

/* Active state styling */
.menu li a.active {
  color: var(--brand-purple);
  font-weight: 500;
}

/* Mobile Dropdown with enhanced styling */
.mobile-dropdown {
  position: relative;
}

.mobile-dropdown-button {
  cursor: pointer;
  transition: transform 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: transparent;
  border: none;
}

.mobile-dropdown-button:hover {
  transform: scale(1.1);
}

.mobile-dropdown-content {
  display: none;
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 95%;
  max-width: 320px;
  margin-top: 0.5rem;
  background-color: white;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-radius: 0.75rem;
  padding: 1rem;
  z-index: 1000;
  transform-origin: top center;
}

/* Enhanced vertical menu styling */
.mobile-dropdown-content .menu {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0;
}

.mobile-dropdown-content .menu li {
  width: 100%;
  margin-bottom: 0.5rem;
  opacity: 0;
  transform: translateY(10px);
  animation: fadeIn 0.3s ease-out forwards, slideDown 0.3s ease-out forwards;
}

/* Staggered animation for menu items */
.mobile-dropdown-content .menu li:nth-child(1) { animation-delay: 0.05s; }
.mobile-dropdown-content .menu li:nth-child(2) { animation-delay: 0.1s; }
.mobile-dropdown-content .menu li:nth-child(3) { animation-delay: 0.15s; }
.mobile-dropdown-content .menu li:nth-child(4) { animation-delay: 0.2s; }
.mobile-dropdown-content .menu li:nth-child(5) { animation-delay: 0.25s; }
.mobile-dropdown-content .menu li:nth-child(6) { animation-delay: 0.3s; }

.mobile-dropdown-content .menu li:last-child {
  margin-bottom: 0;
}

.mobile-dropdown-content .menu li a {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  font-weight: 500;
}

.mobile-dropdown-content .menu li a:hover,
.mobile-dropdown-content .menu li a:focus {
  background-color: rgba(106, 27, 154, 0.1);
  color: var(--brand-purple);
  transform: translateX(5px);
}

.mobile-dropdown-content .menu li a.active {
  background-color: rgba(106, 27, 154, 0.15);
  color: var(--brand-purple);
  font-weight: 600;
  border-left: 3px solid var(--brand-purple);
}

.mobile-dropdown-content.open {
  display: block;
  animation: slideDown 0.3s ease-out, scaleUp 0.3s ease-out;
  transform: translateX(-50%) scale(1);
}

/* Basic styles for nav links and action buttons */
.nav-link {
  @apply block py-2 px-3 text-gray-700 rounded hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700 lg:hover:bg-transparent lg:border-0 lg:hover:text-brand-purple lg:p-0;
  position: relative;
  transition: all 0.3s ease;
}

.action-btn {
  @apply p-2 text-gray-600 rounded-full hover:bg-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 transition-colors duration-300;
}

/* Animated underline for desktop nav links */
.nav-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(90deg, var(--brand-purple) 0%, #9c27b0 100%);
  transition: all 0.3s ease;
  opacity: 0;
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 80%;
  opacity: 1;
}

.nav-link.active {
  color: var(--brand-purple);
  font-weight: 500;
}


/* Hamburger Menu Animation */
.hamburger {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
}

.hamburger span {
  transition: transform 0.3s ease-in-out, opacity 0.2s ease-in-out;
  transform-origin: center;
}

/* Hamburger animation when menu is open */
#nav-toggle:checked + .hamburger span:nth-child(1) {
  transform: translateY(8px) rotate(45deg);
}

#nav-toggle:checked + .hamburger span:nth-child(2) {
  opacity: 0;
}

#nav-toggle:checked + .hamburger span:nth-child(3) {
  transform: translateY(-8px) rotate(-45deg);
}

/* Mobile menu visibility - updated for new structure */
#nav-toggle:checked ~ #nav-menu {
  display: block !important;
}

/* Ensure hamburger is clickable */
.hamburger {
  cursor: pointer;
  user-select: none;
}

/* Mobile menu visibility */
@media (max-width: 1023px) {
  #nav-menu {
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
  }
}

/* Desktop View Adjustments */
@media (min-width: 1024px) {
  .navbar-center {
    display: flex !important;
    align-items: center;
  }

  .mobile-dropdown {
    display: none !important;
  }

  /* Enhanced desktop menu */
  .menu-horizontal li {
    margin: 0 0.25rem;
  }

  .menu-horizontal li a {
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
  }

  .menu-horizontal li a:hover {
    background-color: rgba(106, 27, 154, 0.05);
    transform: translateY(-2px);
  }
}

/* Very small mobile screens */
@media (max-width: 480px) {
  .mobile-dropdown-content {
    width: 85%;
    max-width: 280px;
  }

  .navbar {
    padding: 0.75rem 0;
  }

  /* Ensure mobile menu doesn't cause horizontal overflow */
  #nav-menu {
    right: 0;
    left: auto;
    width: 100%;
    max-width: 320px;
    box-sizing: border-box;
  }

  /* Prevent any element from causing horizontal scroll */
  * {
    max-width: 100%;
    box-sizing: border-box;
  }
}
