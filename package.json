{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "Paw Patroller UK Website", "private": true, "type": "module", "engines": {"node": ">=20.0.0"}, "scripts": {"dev": "npm run copy-assets && vite --host", "dev:watch": "npx nodemon --watch assets --watch src/components --ext html,css,js,json --exec \"npm run copy-assets\" & vite --host", "copy-assets": "node copy-assets.js", "prebuild": "npm run copy-assets", "build": "vite build", "preview": "vite preview", "clean": "rimraf node_modules package-lock.json dist", "audit": "npm audit --production", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "devDependencies": {"@vitest/coverage-v8": "^0.34.6", "autoprefixer": "^10.4.16", "jsdom": "^22.1.0", "postcss": "^8.4.31", "rimraf": "^5.0.1", "tailwindcss": "^3.3.5", "terser": "^5.42.0", "vite": "4.5.2", "vitest": "^0.34.6"}, "dependencies": {"@material-icons/font": "^1.0.36"}, "overrides": {"rollup": "3.29.4"}}