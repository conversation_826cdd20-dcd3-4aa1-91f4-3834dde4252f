<!DOCTYPE html>
<html lang="en" data-theme="light">
  <head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Title | Paw Patroller UK</title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="Description of the page for SEO purposes.">
    <meta name="keywords" content="relevant, keywords, for, this, page">
    <meta name="author" content="Paw Patroller UK">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://pawpatrolleruk.com/page-url.html">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://pawpatrolleruk.com/page-url.html">
    <meta property="og:title" content="Page Title | Paw Patroller UK">
    <meta property="og:description" content="Description of the page for social sharing.">
    <meta property="og:image" content="https://pawpatrolleruk.com/assets/hero-desktop.jpg">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://pawpatrolleruk.com/page-url.html">
    <meta name="twitter:title" content="Page Title | Paw Patroller UK">
    <meta name="twitter:description" content="Description of the page for social sharing.">
    <meta name="twitter:image" content="https://pawpatrolleruk.com/assets/hero-desktop.jpg">

    <!-- Vite entry point -->
    <script type="module" src="/src/main.js"></script>

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  </head>

  <body class="bg-brand-light dark:bg-gray-900">
    <!-- Navbar Container -->
    <div id="navbar-container"></div>

    <!-- Main Content -->
    <main>
      <!-- Page Content Goes Here -->
      <section class="py-16 bg-white dark:bg-gray-800">
        <div class="container px-4 mx-auto">
          <div class="text-center max-w-3xl mx-auto mb-16">
            <span class="inline-block px-3 py-1 text-xs font-semibold tracking-wider text-brand-purple dark:text-brand-peach bg-brand-purple/10 dark:bg-brand-purple/20 rounded-full uppercase mb-4">Section Label</span>
            <h1 class="text-3xl sm:text-4xl md:text-5xl font-fredoka gradient-text mb-6">Page Title</h1>
            <p class="text-gray-600 dark:text-gray-300 text-lg leading-relaxed">Page description or introduction text goes here.</p>
          </div>

          <!-- Page content sections go here -->

        </div>
      </section>
    </main>

    <!-- Footer Container -->
    <div id="footer-container"></div>

    <!-- Floating Buttons Container -->
    <div id="floating-buttons-container"></div>

    <!-- Cookie Banner Container -->
    <div id="cookie-banner-container"></div>
  </body>
</html>
