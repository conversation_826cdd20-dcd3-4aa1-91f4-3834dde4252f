/**
 * Component styles
 */

/* Hero section */
.hero {
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
}

/* Badge styles */
.badge-card {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 150px;
  min-height: 60px;
}

.badge-card img {
  transition: transform 0.3s ease;
}

.badge-card:hover img {
  transform: scale(1.05);
}

@media screen and (max-width: 480px) {
  .badge-card {
    min-width: 120px;
    min-height: 50px;
  }
}

/* Cookie consent styles */
.cookie-banner {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  z-index: 1000;
  display: none; /* Hidden by default, shown via JS */
  transition: var(--theme-transition);
  pointer-events: auto; /* Ensure it's always clickable */
  transform: translateY(0); /* Ensure it's visible when shown */
  opacity: 1; /* Ensure it's fully visible */
  visibility: visible; /* Ensure it's visible */
}

.dark .cookie-banner {
  background: var(--bg-color-secondary);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
}

.cookie-preferences {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1100;
  display: none; /* Hidden by default, shown via JS */
}

.cookie-preferences-content {
  background: white;
  padding: 2rem;
  border-radius: 0.5rem;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  transition: var(--theme-transition);
}

.dark .cookie-preferences-content {
  background: var(--bg-color-secondary);
  color: var(--text-color);
}

.cookie-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  min-height: 44px; /* Ensure touch-friendly size */
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.cookie-btn-primary {
  background: var(--brand-purple, #6a1b9a);
  color: white;
}

.cookie-btn-primary:hover {
  background: var(--brand-purple-dark, #4a148c);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(106, 27, 154, 0.3);
}

.dark .cookie-btn-primary {
  background: var(--brand-purple-dark, #4a148c);
  color: white;
}

.cookie-btn-secondary {
  background: #f0f0f0;
  color: #333;
}

.dark .cookie-btn-secondary {
  background: #333;
  color: #f0f0f0;
}

.cookie-btn-outline {
  background: transparent;
  border: 1px solid var(--brand-purple, #6a1b9a);
  color: var(--brand-purple, #6a1b9a);
}

.dark .cookie-btn-outline {
  border: 1px solid var(--brand-peach, #ffccbc);
  color: var(--brand-peach, #ffccbc);
}

/* Improved cookie banner for mobile */
@media screen and (max-width: 480px) {
  .cookie-banner {
    padding: 1rem 0.5rem;
  }

  .cookie-banner h3 {
    font-size: 1rem;
  }

  .cookie-banner p {
    font-size: 0.875rem;
  }
}

/* Fix for service cards on small screens */
.card {
  height: auto;
  display: flex;
  flex-direction: column;
  transition: var(--theme-transition);
}

.dark .card {
  background-color: var(--bg-color-secondary);
  color: var(--text-color);
  border-color: var(--border-color);
}

/* Make details summary more touch-friendly */
details summary {
  padding: 0.5rem 0;
  cursor: pointer;
  transition: var(--theme-transition);
}

.dark details summary {
  color: var(--text-color);
}

/* Dark mode specific styles */
.dark .bg-white {
  background-color: var(--bg-color) !important;
}

.dark .text-gray-700 {
  color: var(--text-color) !important;
}

.dark .border-gray-200 {
  border-color: var(--border-color) !important;
}

/* Floating buttons styles */

/* WhatsApp button pulse animation */
@keyframes whatsapp-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
  }
}

.whatsapp-pulse {
  animation: whatsapp-pulse 2s infinite;
}

/* Floating button hover effects */
.floating-button-group {
  perspective: 1000px;
}

.floating-button-group button,
.floating-button-group a {
  transform-style: preserve-3d;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.floating-button-group button:hover,
.floating-button-group a:hover {
  transform: translateY(-2px) rotateY(5deg);
}

/* Mobile Accessibility Enhancements */
@media screen and (max-width: 768px) {
  /* Ensure all clickable elements meet minimum touch target size */
  .btn,
  button,
  a[href],
  input,
  textarea,
  select {
    min-height: 44px;
    min-width: 44px;
    padding: 0.75rem;
  }

  /* Improve mobile navigation */
  .mobile-dropdown-button {
    min-height: 48px;
    min-width: 48px;
    padding: 0.75rem;
  }

  /* Better mobile menu spacing */
  .mobile-dropdown-content .menu li a {
    padding: 1rem;
    font-size: 1.1rem;
    line-height: 1.4;
  }

  /* Improve form controls on mobile */
  input[type='text'],
  input[type='email'],
  input[type='tel'],
  textarea {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 1rem;
  }

  /* Better spacing for mobile cards */
  .card {
    margin-bottom: 1.5rem;
    padding: 1.5rem;
  }

  /* Improve floating buttons for mobile */
  .floating-button-group a,
  .floating-button-group button {
    min-height: 56px;
    min-width: 56px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn,
  button {
    border: 2px solid currentColor;
  }

  .text-brand-purple {
    color: #000 !important;
  }

  .bg-brand-purple {
    background-color: #000 !important;
    color: #fff !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .animate-pulse,
  .animate-bounce,
  .whatsapp-pulse {
    animation: none !important;
  }
}

/* Focus improvements for keyboard navigation */
.btn:focus,
button:focus,
a:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 3px solid var(--brand-purple);
  outline-offset: 2px;
}

/* Skip link for screen readers */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--brand-purple);
  color: white;
  padding: 8px;
  text-decoration: none;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}
