/**
 * Base styles for the website
 */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Variables */
:root {
  /* Light theme colors (default) */
  --brand-purple: #6a1b9a;
  --brand-purple-dark: #4a148c;
  --brand-light: #f5f5f5;
  --brand-peach: #ffccbc;

  /* Common colors */
  --text-color: #333333;
  --bg-color: #ffffff;
  --bg-color-secondary: #f5f5f5;
  --border-color: #e0e0e0;
  --shadow-color: rgba(0, 0, 0, 0.1);

  /* Theme transition */
  --theme-transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Dark theme colors */
.dark {
  --text-color: #e0e0e0;
  --bg-color: #121212;
  --bg-color-secondary: #1e1e1e;
  --border-color: #333333;
  --shadow-color: rgba(0, 0, 0, 0.3);
}

/* Base Element Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  overflow-x: hidden; /* Prevent horizontal scrolling at root level */
}

@supports (-webkit-overflow-scrolling: touch) {
  html {
    scroll-behavior: auto;
  }
}

body {
  font-family: 'Poppins', sans-serif;
  margin: 0;
  min-width: 320px;
  max-width: 100vw; /* Ensure body doesn't exceed viewport width */
  color: var(--text-color);
  background-color: var(--bg-color);
  transition: var(--theme-transition);
  overflow-x: hidden; /* Prevent horizontal scrolling */
}

/* Button styles */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 font-medium transition-all duration-300 rounded cursor-pointer;
}

.btn-ghost {
  @apply text-gray-700 bg-transparent hover:bg-gray-100;
}

.btn-circle {
  @apply flex items-center justify-center w-10 h-10 p-0 rounded-full;
}

button {
  cursor: pointer;
}

.divider {
  @apply h-px my-2 bg-gray-200;
}

/* Ensure containers don't cause overflow */
.container {
  width: 100%;
  max-width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
    padding-left: 4rem;
    padding-right: 4rem;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

/* Utility Classes */
.gradient-text {
  background: linear-gradient(90deg, var(--brand-purple) 0%, #9c27b0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* Fix for gradient text getting cut off */
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  line-height: 1.3;
  display: inline-block; /* Ensures the background covers the entire text */
  width: 100%;
}

/* Fix for section headings */
h2.gradient-text {
  margin-top: 0.5rem;
  margin-bottom: 1.5rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

/* Responsive adjustments for headings */
@media screen and (max-width: 640px) {
  h2.gradient-text {
    line-height: 1.4;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    word-break: break-word;
  }
}

/* Screen reader only utility class */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Skip link styling */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  z-index: 1000;
  border-radius: 4px;
}

.skip-link:focus {
  top: 6px;
}
