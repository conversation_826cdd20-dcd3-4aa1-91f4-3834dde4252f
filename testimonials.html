<!DOCTYPE html>
<html lang="en" data-theme="light">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Testimonials - Paw Patroller UK</title>

    <!-- Essential SEO -->
    <meta name="description" content="Read what our clients have to say about Paw Patroller UK's dog care services. Real testimonials from satisfied pet owners.">
    <meta name="keywords" content="dog care testimonials, pet sitting reviews, dog walking service reviews">

    <!-- Essential Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Fredoka:wght@400;500;600&family=Poppins:wght@400;600;700&family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.cdnfonts.com/css/agent-orange" rel="stylesheet">
    <link href="https://fonts.cdnfonts.com/css/gracility" rel="stylesheet">

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Styles -->
    <link rel="stylesheet" href="src/css/index.css">

    <!-- Vite entry point -->
    <script type="module" src="src/main.js"></script>
  </head>
  <body class="bg-brand-light">

    <!-- Navbar Container -->
    <div id="navbar-container"></div>

    <main>
      <section id="testimonials" class="py-24 bg-white">
        <div class="container px-4 mx-auto">
          <h2 class="mb-16 text-4xl text-center font-fredoka md:text-5xl gradient-text">What Our Clients Say</h2>

          <div class="review-carousel-container">
            <!-- Horizontally Scrollable Review Carousel -->
            <div id="all-reviews" class="review-carousel">
              <!-- Testimonials loaded here via JavaScript -->
            </div>

            <!-- Scroll Indicator -->
            <div class="scroll-indicator">
              <span class="material-icons mr-2">swipe</span>
              <span>Swipe to see more reviews</span>
            </div>

            <!-- Pagination Dots -->
            <div class="pagination-dots" id="all-pagination-dots">
              <!-- Pagination dots will be added dynamically -->
            </div>

            <!-- Carousel Navigation -->
            <div class="carousel-nav">
              <button id="all-prev-button" class="carousel-button" disabled>
                <span class="material-icons">arrow_back</span>
              </button>
              <button id="all-next-button" class="carousel-button">
                <span class="material-icons">arrow_forward</span>
              </button>
            </div>
          </div>

          <div class="mt-8 text-center">
            <a href="index.html" class="inline-block px-6 py-3 text-lg font-medium text-white transition-all duration-300 transform rounded-full bg-brand-purple hover:bg-brand-purple-dark hover:shadow-lg hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-brand-purple focus:ring-opacity-50 group">
              Back to Home
              <span class="ml-2 transition-transform material-icons group-hover:-translate-x-1">arrow_back</span>
            </a>
          </div>
        </div>
      </section>
    </main>

    <!-- Footer Container -->
    <div id="footer-container"></div>

    <!-- Floating Buttons Container -->
    <div id="floating-buttons-container"></div>

    <!-- Cookie Banner Container -->
    <div id="cookie-banner-container"></div>

    <script>
      document.addEventListener('DOMContentLoaded', function() {
        const allReviewsContainer = document.getElementById('all-reviews');
        if (!allReviewsContainer) return;

        // Set current year for copyright
        document.querySelectorAll('#current-year').forEach(el => {
          el.textContent = new Date().getFullYear();
        });

        // Load reviews with star rating system
        fetch('data/reviews.json') // Updated path to use public directory
          .then(response => response.json())
          .then(data => {
            if (!Array.isArray(data)) {
              throw new Error('Invalid data format');
            }

            data.forEach((review, index) => {
              // Sanitize data
              const safeText = (review.review_text || '').replace(/</g, '&lt;').replace(/>/g, '&gt;');
              const safeName = (review.reviewer_name || 'Anonymous').replace(/</g, '&lt;').replace(/>/g, '&gt;');

              // Generate stars based on rating (default to 5 if not specified)
              const rating = review.rating || 5;
              let starsHTML = '';
              for (let i = 1; i <= 5; i++) {
                starsHTML += `<span class="star">${i <= rating ? '★' : '☆'}</span>`;
              }

              // Create review element with new design
              const reviewElement = document.createElement('div');
              reviewElement.className = 'review-card';
              reviewElement.setAttribute('data-index', index);

              // Determine what to show at the top (image or placeholder)
              let imageSection = '';
              if (review.image_url) {
                imageSection = `<img src="reviews/${review.image_url}" alt="Review from ${safeName}" class="review-image" onerror="this.onerror=null;this.src='reviews/default-review.jpg';">`;
              } else {
                imageSection = `
                  <div class="review-image-placeholder">
                    <span class="material-icons">pets</span>
                  </div>
                `;
              }

              reviewElement.innerHTML = `
                ${imageSection}
                <div class="review-stars">
                  ${starsHTML}
                </div>
                <div class="review-content">
                  <div class="review-header">
                    <span class="review-name">${safeName}</span>
                    ${review.verified_booking ? '<span class="review-badge">Verified</span>' : ''}
                  </div>
                  <p class="review-text">${safeText}</p>
                </div>
              `;

              allReviewsContainer.appendChild(reviewElement);
            });

            // Set up pagination
            setupAllPagination(data.length);

            // Initialize carousel
            initAllCarouselNavigation();
          })
          .catch(error => {
            console.error('Error loading reviews:', error);
            allReviewsContainer.innerHTML = '<div class="col-span-full text-center p-8"><p>Sorry, we couldn\'t load our testimonials at the moment. Please check back later!</p></div>';
          });

        // Set up pagination dots based on number of reviews
        function setupAllPagination(totalReviews) {
          const paginationContainer = document.getElementById('all-pagination-dots');
          if (!paginationContainer) return;

          // Calculate how many pages we need based on viewport size
          let itemsPerPage = 1; // Mobile default
          if (window.innerWidth >= 768) itemsPerPage = 2; // Tablet
          if (window.innerWidth >= 1024) itemsPerPage = 3; // Desktop

          const totalPages = Math.ceil(totalReviews / itemsPerPage);

          // Create pagination dots
          paginationContainer.innerHTML = '';
          for (let i = 0; i < totalPages; i++) {
            const dot = document.createElement('div');
            dot.className = `pagination-dot ${i === 0 ? 'active' : ''}`;
            dot.setAttribute('data-page', i);
            dot.addEventListener('click', () => scrollToAllPage(i));
            paginationContainer.appendChild(dot);
          }
        }

        // Scroll to specific page for all reviews
        function scrollToAllPage(pageIndex) {
          if (!allReviewsContainer) return;

          // Calculate width based on viewport
          let itemsPerPage = 1; // Mobile default
          if (window.innerWidth >= 768) itemsPerPage = 2; // Tablet
          if (window.innerWidth >= 1024) itemsPerPage = 3; // Desktop

          // Find the card at the start of the requested page
          const cardIndex = pageIndex * itemsPerPage;
          const card = allReviewsContainer.querySelector(`[data-index="${cardIndex}"]`);          if (card) {
            // Scroll to the card
            allReviewsContainer.scrollTo({
              left: card.offsetLeft - allReviewsContainer.offsetLeft,
              behavior: 'auto'
            });

            // Update the active dot
            updateAllActiveDot(pageIndex);

            // Update navigation buttons
            updateAllNavigationButtons(pageIndex);
          }
        }

        // Update active pagination dot
        function updateAllActiveDot(activeIndex) {
          const dots = document.querySelectorAll('#all-pagination-dots .pagination-dot');
          dots.forEach((dot, index) => {
            dot.classList.toggle('active', index === activeIndex);
          });
        }

        // Update navigation button states
        function updateAllNavigationButtons(currentPageIndex) {
          const prevButton = document.getElementById('all-prev-button');
          const nextButton = document.getElementById('all-next-button');
          const totalPages = document.querySelectorAll('#all-pagination-dots .pagination-dot').length;

          if (prevButton) {
            prevButton.disabled = currentPageIndex === 0;
          }

          if (nextButton) {
            nextButton.disabled = currentPageIndex === totalPages - 1;
          }
        }

        // Initialize carousel navigation for all reviews
        function initAllCarouselNavigation() {
          const prevButton = document.getElementById('all-prev-button');
          const nextButton = document.getElementById('all-next-button');

          if (!allReviewsContainer || !prevButton || !nextButton) return;

          // Handle previous button click
          prevButton.addEventListener('click', () => {
            const dots = document.querySelectorAll('#all-pagination-dots .pagination-dot');
            const activeDotIndex = Array.from(dots).findIndex(dot => dot.classList.contains('active'));

            if (activeDotIndex > 0) {
              scrollToAllPage(activeDotIndex - 1);
            }
          });

          // Handle next button click
          nextButton.addEventListener('click', () => {
            const dots = document.querySelectorAll('#all-pagination-dots .pagination-dot');
            const activeDotIndex = Array.from(dots).findIndex(dot => dot.classList.contains('active'));

            if (activeDotIndex < dots.length - 1) {
              scrollToAllPage(activeDotIndex + 1);
            }
          });

          // Handle scroll events to update the active dot
          allReviewsContainer.addEventListener('scroll', () => {
            // Use requestAnimationFrame to limit events
            requestAnimationFrame(() => {
              // Calculate which page we're on based on scroll position
              const containerWidth = allReviewsContainer.offsetWidth;
              const scrollPosition = allReviewsContainer.scrollLeft;

              let itemsPerPage = 1; // Mobile default
              if (window.innerWidth >= 768) itemsPerPage = 2; // Tablet
              if (window.innerWidth >= 1024) itemsPerPage = 3; // Desktop

              // Calculate card width (including gap)
              const cardWidth = containerWidth / itemsPerPage;

              // Calculate which page we're on
              const currentPage = Math.round(scrollPosition / cardWidth);

              updateAllActiveDot(currentPage);
              updateAllNavigationButtons(currentPage);
            });
          });
        }

        // Update on window resize
        window.addEventListener('resize', () => {
          // Recalculate pagination on resize
          const totalReviews = document.querySelectorAll('#all-reviews .review-card').length;
          setupAllPagination(totalReviews);

          // Reset to first page on resize
          scrollToAllPage(0);
        });
      });
    </script>
  </body>
</html>
